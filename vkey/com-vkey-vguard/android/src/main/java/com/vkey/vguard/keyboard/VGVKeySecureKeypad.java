package com.vkey.vguard.keyboard;

import com.vkey.android.secure.keyboard.VKeySecureKeypad;

public class VGVKeySecureKeypad {
    public static void setVKSecureKeyboardLayout(int VKSecureKeyboardLayout) {
        VKeySecureKeypad.VKSecureKeyboardLayout = VKSecureKeyboardLayout;
    }
    
    public static void setQwertyLayout(int qwertyLayout) {
        VKeySecureKeypad.qwertyLayout = qwertyLayout;
    }
    
    public static void setQwertyCapsLayout(int qwertyCapsLayout) {
        VKeySecureKeypad.qwertyCapsLayout = qwertyCapsLayout;
    }

    public static void setNumbersSymbolsLayout(int numbersSymbolsLayout) {
        VKeySecureKeypad.numbersSymbolsLayout = numbersSymbolsLayout;
    }

    public static void setNumbersSymbolsLayout2(int numbersSymbolsLayout2) {
        VKeySecureKeypad.numbersSymbolsLayout2 = numbersSymbolsLayout2;
    }

    public static void setNumbersLayout(int numbersLayout) {
        VKeySecureKeypad.numbersLayout = numbersLayout;
    }

    public static void setVKSecureEditTextAttrs(int[] VKSecureEditTextAttrs) {
        VKeySecureKeypad.VKSecureEditTextAttrs = VKSecureEditTextAttrs;
    }

    public static void setVKSecureEditTextInDialogIdx(int VKSecureEditTextInDialogIdx) {
        VKeySecureKeypad.VKSecureEditTextInDialogIdx = VKSecureEditTextInDialogIdx;
    }

    public static void setVKSecureEditTextRandomizedIdx(int VKSecureEditTextInDialogIdx) {
        VKeySecureKeypad.VKSecureEditTextInDialogIdx = VKSecureEditTextInDialogIdx;
    }
}

