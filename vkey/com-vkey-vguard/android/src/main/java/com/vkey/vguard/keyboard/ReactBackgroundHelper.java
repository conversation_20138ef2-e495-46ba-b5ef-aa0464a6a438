package com.vkey.vguard.keyboard;

import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.View;

import androidx.annotation.Nullable;

public class ReactBackgroundHelper {
    private final View view;
    private final GradientDrawable drawable = new GradientDrawable();

    private float borderWidth = 0;
    private int borderColor = Color.TRANSPARENT;
    private float[] cornerRadii = new float[8];
    private @Nullable String borderStyle = null;

    public ReactBackgroundHelper(View view) {
        this.view = view;
        view.setBackground(drawable);
    }

    public void setBackgroundColor(int color) {
        drawable.setColor(color);
        update();
    }

    public void setBorderWidth(int position, float width) {
        // Uniform only supported; store the last used
        this.borderWidth = width;
        update();
    }

    public void setBorderColor(int position, float color, float alpha) {
        int argbColor = ((int)(alpha * 255) << 24) | (0x00FFFFFF & (int) color);
        this.borderColor = argbColor;
        update();
    }

    public void setBorderRadius(float borderRadius) {
        for (int i = 0; i < 8; i++) {
            cornerRadii[i] = borderRadius;
        }
        update();
    }

    public void setBorderRadius(float borderRadius, int position) {
        // Positions: 0 = top-left, 1 = top-right, 2 = bottom-right, 3 = bottom-left
        int index = position * 2;
        if (index >= 0 && index + 1 < cornerRadii.length) {
            cornerRadii[index] = borderRadius;
            cornerRadii[index + 1] = borderRadius;
        }
        update();
    }

    public void setBorderStyle(@Nullable String style) {
        this.borderStyle = style;
        update();
    }

    private void update() {
        drawable.setCornerRadii(cornerRadii);
        if ("dashed".equals(borderStyle)) {
            drawable.setStroke((int) borderWidth, borderColor, 10f, 10f);
        } else {
            drawable.setStroke((int) borderWidth, borderColor);
        }
        view.invalidate();
    }
}

