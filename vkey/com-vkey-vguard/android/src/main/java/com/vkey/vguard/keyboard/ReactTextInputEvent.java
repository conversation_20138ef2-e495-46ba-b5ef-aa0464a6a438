package com.vkey.vguard.keyboard;

import androidx.annotation.Nullable;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.uimanager.events.Event;

public class ReactTextInputEvent extends Event<ReactTextInputEvent> {
    public static final String EVENT_NAME = "topTextInput";
    private String mText;
    private String mPreviousText;
    private int mRangeStart;
    private int mRangeEnd;

    public ReactTextInputEvent(int viewId, String text, String previousText, int rangeStart, int rangeEnd) {
        this(-1, viewId, text, previousText, rangeStart, rangeEnd);
    }

    public ReactTextInputEvent(int surfaceId, int viewId, String text, String previousText, int rangeStart, int rangeEnd) {
        super(surfaceId, viewId);
        this.mText = text;
        this.mPreviousText = previousText;
        this.mRangeStart = rangeStart;
        this.mRangeEnd = rangeEnd;
    }

    public String getEventName() {
        return "topTextInput";
    }

    public boolean canCoalesce() {
        return false;
    }

    @Nullable
    protected WritableMap getEventData() {
        WritableMap eventData = Arguments.createMap();
        WritableMap range = Arguments.createMap();
        range.putDouble("start", (double)this.mRangeStart);
        range.putDouble("end", (double)this.mRangeEnd);
        eventData.putString("text", this.mText);
        eventData.putString("previousText", this.mPreviousText);
        eventData.putMap("range", range);
        eventData.putInt("target", this.getViewTag());
        return eventData;
    }
}
