
package com.vkey.vguard;

import static com.vkey.android.vguard.VGuardBroadcastReceiver.ACTION_FINISH;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.ACTION_SCAN_COMPLETE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.PROFILE_LOADED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.SCAN_COMPLETE_LEVEL;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.SCAN_COMPLETE_RESULT;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_AIRDROID_PORT_IS_OPEN;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_ALERT_MESSAGE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_ALERT_TITLE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_DEVELOPER_OPTIONS_ENABLED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_DISABLED_APP_EXPIRED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_HANDLE_THREAT_POLICY;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_MESSAGE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_NETWORK_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_OVERLAY_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_OVERLAY_DETECTED_DISABLE;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_SCREEN_SHARING_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_SSL_ERROR_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_STATUS;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_VIRTUAL_SPACE_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VGUARD_VIRTUAL_TAP_DETECTED;
import static com.vkey.android.vguard.VGuardBroadcastReceiver.VOS_READY;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.modules.core.DeviceEventManagerModule;
import com.vkey.android.internal.vguard.engine.BasicThreatInfo;
import com.vkey.android.secure.keyboard.VKeySecureKeypad;
import com.vkey.android.vguard.ActivityLifecycleHook;
import com.vkey.android.vguard.FeatureToggleManager;
import com.vkey.android.vguard.LocalBroadcastManager;
import com.vkey.android.vguard.MemoryConfiguration;
import com.vkey.android.vguard.VGExceptionHandler;
import com.vkey.android.vguard.VGuard;
import com.vkey.android.vguard.VGuardBroadcastReceiver;
import com.vkey.android.vguard.VGuardFactory;
import com.vkey.android.vguard.VGuardLifecycleHook;
import com.vkey.android.vguard.model.VGScanLevel;
import com.vkey.android.vguard.model.VGSecurityStatus;
import com.vkey.android.vguard.model.VGThreatAppInfo;
import com.vkey.android.vguard.model.VGThreatPolicy;
import com.vkey.android.vguard.model.VGThreatResponse;
import com.vkey.android.vguard.model.VGVirtualTapType;
import com.vkey.android.vguard.model.VGuardNetworkType;
import com.vkey.vguard.common.Utility;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import vkey.android.vos.VosWrapper;

public class VGuardPlugin extends ReactContextBaseJavaModule {

  @NonNull
  @Override
  public String getName() {
    return "VGuardPlugin";
  }

  private static final String TAG = VGuardPlugin.class.getName();
  private static ReactApplicationContext reactContext;
  private static WeakReference<Activity> currentActivity;
  private static final int ERROR = -1;
  private static final String VGUARD_EVENTS = "vkey.android.vguard.events";
  private static final String VGUARD_ERROR = "VGUARD_ERROR";
  private static final String FIRMWARE_RETURN_CODE = "vkey.android.vguard.FIRMWARE_RETURN_CODE";
  public static final String RESET_VOS_STORAGE = "vkey.android.vguard.resetVOSTrustedStorageRvcr";
  // For VGuard to notify host app of events
  private static VGuardBroadcastReceiver mVGuardRcvr;

  // VGuard object that is used for scanning
  private static VGuard vGuardMgr;
  private final static long VOS_INIT = 1;
  private static long vosFirmwareCode = VOS_INIT;
  // LifecycleHook to notify VGuard of activity's lifecycle
  public static VGuardLifecycleHook hook;

  private MemoryConfiguration mMemoryConfiguration = MemoryConfiguration.DEFAULT;
  private boolean isAllowsArbitraryNetworking = false;
  private String tlaUrl = null;
  private String tiUrl = null;
  private boolean isDebug = false;
  private static int activityCount = 0;
  private boolean isOverlayDetectionEnabled = false;
  private boolean isVirtualTapDetectionEnabled = false;
  private boolean isUsePackageManagerEnabled = true;

  static {
    VKeySecureKeypad.VKSecureKeyboardLayout = R.xml.vk_input1;
    VKeySecureKeypad.VKSecureEditTextAttrs = R.styleable.VKSecureEditText;
    VKeySecureKeypad.VKSecureEditTextInDialogIdx = R.styleable.VKSecureEditText_inDialog;
    VKeySecureKeypad.VKSecureEditTextRandomizedIdx = R.styleable.VKSecureEditText_randomized;
    VKeySecureKeypad.qwertyLayout = R.xml.vk_keyboard_qwerty;
    VKeySecureKeypad.qwertyCapsLayout = R.xml.vk_keyboard_qwerty_caps;
    VKeySecureKeypad.numbersSymbolsLayout = R.xml.vk_keyboard_numbers_symbols;
    VKeySecureKeypad.numbersSymbolsLayout2 = R.xml.vk_keyboard_numbers_symbols2;
    VKeySecureKeypad.numbersLayout = R.xml.vk_keyboard_numbers;
    VKeySecureKeypad.numbersLayoutHorizontal = R.xml.vk_keyboard_numbers_symbol_horizontal;
  }

  public VGuardPlugin(ReactApplicationContext reactContext) {
    super(reactContext);
    VGuardPlugin.reactContext = reactContext;
  }

  public static void setCurrentActivity(Activity activity) {
    if(activity != null) {
      currentActivity = new WeakReference<>(activity);
    }
  }

  public static void setVGActivityLifecycleCallbacks(Application application) {
    if(application != null){
      application.registerActivityLifecycleCallbacks(vgActivityLifecycleCallbacks);
    }
  }

  private static final VGActivityLifecycleCallbacks vgActivityLifecycleCallbacks = new VGActivityLifecycleCallbacks() {
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
      super.onActivityCreated(activity, savedInstanceState);
      setCurrentActivity(activity);
      activityCount++;
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
      setCurrentActivity(activity);
      if (vGuardMgr != null && hook != null ) {
        try {
          vGuardMgr.onResume(hook, reactContext);
        } catch (Exception e) {
          e.printStackTrace();
        }
      }
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {
      Log.d(TAG, "LifecycleIssue onActivityPaused");
      if (vGuardMgr != null && hook != null) {
        try {
          vGuardMgr.onPause(hook);
        } catch (Exception e) {
          Log.e(TAG, "vGuardMgr.onPause throw exception causes: " + e.getMessage());
        }
      }
    }

    /**
     * Because onActivityDestroyed is called continuously,
     * it will destroy the VGuard if we invoke the onDestroy method here,
     * so we need to check the host activity is true to destroy VGuard.
     */
    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
      activityCount--;
      if(activityCount <= 0) {
        _vguardDestroy();
        activityCount = 0;
      }
    }
  };

  static void _vguardDestroy() {
    if (currentActivity != null && currentActivity.get().isDestroyed()){
      if(mVGuardRcvr != null) {
        LocalBroadcastManager.getInstance(reactContext).unregisterReceiver(mVGuardRcvr);
        mVGuardRcvr = null;
      }

      vosFirmwareCode = VOS_INIT;
      if (vGuardMgr != null) {
        try {
          vGuardMgr.destroy();
        } catch (Exception e) {
          Log.e(TAG, "vGuardMgr.onDestroy throw exception causes: " + e.getMessage());
        }
        vGuardMgr = null;
      }
    }
  }

  static boolean isVosStarted() {
    if(vGuardMgr != null) {
      return vGuardMgr.getIsVosStarted();
    }
    return false;
  }

  @javax.annotation.Nullable
  @Override
  public Map<String, Object> getConstants() {
    final Map<String, Object> constants = new HashMap<>();
    constants.put("VGUARD_EVENTS", VGUARD_EVENTS);
    constants.put("VGUARD_ERROR", VGUARD_ERROR);
    constants.put("VOS_READY", VOS_READY);
    constants.put("VGUARD_STATUS", VGUARD_STATUS);
    constants.put("VGUARD_OVERLAY_DETECTED", VGUARD_OVERLAY_DETECTED);
    constants.put("VGUARD_OVERLAY_DETECTED_DISABLE", VGUARD_OVERLAY_DETECTED_DISABLE);
    constants.put("VGUARD_PROFILE_LOADED", PROFILE_LOADED);

    // scan threats
    constants.put("ACTION_FINISH", ACTION_FINISH);
    constants.put("ACTION_SCAN_COMPLETE", ACTION_SCAN_COMPLETE);

    // 4.9
    constants.put("RESET_VOS_STORAGE", RESET_VOS_STORAGE);
    constants.put("VGUARD_VIRTUAL_SPACE_DETECTED", VGUARD_VIRTUAL_SPACE_DETECTED);
    constants.put("VGUARD_SCREEN_SHARING_DETECTED", VGUARD_SCREEN_SHARING_DETECTED);
    constants.put("VGUARD_SSL_ERROR_DETECTED", VGUARD_SSL_ERROR_DETECTED);
    constants.put("VGUARD_HANDLE_THREAT_POLICY", VGUARD_HANDLE_THREAT_POLICY);
    constants.put("VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED", VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED);

    //4.10
    constants.put("VGUARD_DEVELOPER_OPTIONS_ENABLED", VGUARD_DEVELOPER_OPTIONS_ENABLED);
    constants.put("VGUARD_AIRDROID_PORT_IS_OPEN", VGUARD_AIRDROID_PORT_IS_OPEN);
    constants.put("VGUARD_VIRTUAL_TAP_DETECTED", VGUARD_VIRTUAL_TAP_DETECTED);
    constants.put("VGUARD_NETWORK_DETECTED", VGUARD_NETWORK_DETECTED);
    constants.put("SCAN_COMPLETE_LEVEL", SCAN_COMPLETE_LEVEL);

    return constants;
  }

  @ReactMethod
  public void forceSyncLogs() {
    if(!TextUtils.isEmpty(tlaUrl)) {
      VosWrapper.getInstance(reactContext).forceSyncLogs();
    }
  }

  @ReactMethod
  public void setLoggerBaseUrl(String url) {
    tlaUrl = url;
    VosWrapper.getInstance(reactContext).setLoggerBaseUrl(url);
  }

  @ReactMethod
  public void setThreatIntelligenceServerURL(String threadIntelUrl) {
    tiUrl = threadIntelUrl;
  }

  @ReactMethod
  public void setDebugable(boolean debugable) {
    this.isDebug = debugable;
    VGuardFactory.debug = debugable;
  }

  @ReactMethod
  public void setAllowsArbitraryNetworking(boolean enable) {
    isAllowsArbitraryNetworking = enable;
    if (vGuardMgr != null) {
      vGuardMgr.allowsArbitraryNetworking(enable);
    }
  }

  @ReactMethod
  public void setMemoryConfiguration(int config) {
    mMemoryConfiguration = config == 0? MemoryConfiguration.DEFAULT: MemoryConfiguration.HIGH;
    if (vGuardMgr != null) {
      vGuardMgr.setMemoryConfiguration(mMemoryConfiguration);
    }
  }

  @ReactMethod
  public void setMaximumNetworkRetryTime(int retry) {
    if (vGuardMgr != null) {
      vGuardMgr.setMaximumNetworkRetryTime(retry);
    }
  }

  @ReactMethod
  public boolean clearVOSTrustedStorage(){
    if (vGuardMgr != null) {
      return vGuardMgr.clearVOSTrustedStorage();
    }
    return false;
  }

  @ReactMethod
  public boolean resetVOSTrustedStorage() {
    if (vGuardMgr != null) {
      vGuardMgr.resetVOSTrustedStorage();
    }
    return false;
  }

  /**
   * This method is only used on Android platform.
   */
  @ReactMethod
  public void setOverlayDetectionEnabled(boolean isEnable){
    this.isOverlayDetectionEnabled = isEnable;
  }

  @ReactMethod
  public void destroy() {
    _vguardDestroy();
  }

  @ReactMethod
  public void setupVGuard() {
    Log.d(TAG, "setupVGuard called");

    // register using LocalBroadcastManager only for keeping data within your app
    if(mVGuardRcvr == null) {
      vosFirmwareCode = VOS_INIT;
      mVGuardRcvr = initialVGuardReceiver();

      VGuardBroadcastReceiver resetVOSTrustedStorageRvcr = new VGuardBroadcastReceiver(null) {
        @Override
        public void onReceive(Context context, Intent intent) {
          super.onReceive(context, intent);
          boolean isResetVOSTrustedStorageSuccess = vGuardMgr.resetVOSTrustedStorage();
          sendEventEmitter(reactContext, RESET_VOS_STORAGE, isResetVOSTrustedStorageSuccess);
        }
      };
      LocalBroadcastManager localBroadcastMgr = LocalBroadcastManager.getInstance(reactContext);
      // necessary for vguard to finish activity safely
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(ACTION_FINISH));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(ACTION_SCAN_COMPLETE));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VOS_READY));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(PROFILE_LOADED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_OVERLAY_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_OVERLAY_DETECTED_DISABLE));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_STATUS));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_VIRTUAL_SPACE_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_SCREEN_SHARING_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_DEVELOPER_OPTIONS_ENABLED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_AIRDROID_PORT_IS_OPEN));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_VIRTUAL_TAP_DETECTED));
      localBroadcastMgr.registerReceiver(mVGuardRcvr, new IntentFilter(VGUARD_NETWORK_DETECTED));

      localBroadcastMgr.registerReceiver(resetVOSTrustedStorageRvcr, new IntentFilter(RESET_VOS_STORAGE));
    }
    invokeVGuard();
  }

  private final VGExceptionHandler vgExceptionHandler = e -> {
    getVGuardInstance();
    String errorCode = e.getMessage();
    if("20035".equalsIgnoreCase(errorCode)
       || "-3".equalsIgnoreCase(errorCode)
            || "-5".equalsIgnoreCase(errorCode)) {
      vGuardMgr.resetVOSTrustedStorage();
      vGuardMgr.destroy();
      invokeVGuard();
    }
    else {
      Log.e(TAG, "A serious exception has occurred within V-Guard", e);
      sendEventEmitter(reactContext, VGUARD_ERROR, e.getMessage());
    }
  };

  private void invokeVGuard(){
    try {
      VGuardFactory.Builder builder = new VGuardFactory.Builder();
      builder.setDebugable(isDebug)
              .setMemoryConfiguration(mMemoryConfiguration)
              .setVGExceptionHandler(vgExceptionHandler)
              .setAllowsArbitraryNetworking(isAllowsArbitraryNetworking)
              .setVirtualTapDetectionEnabled(isVirtualTapDetectionEnabled)
              .setUsePackageManager(isUsePackageManagerEnabled);
      //Overlay Detection
      FeatureToggleManager featureToggleManager = FeatureToggleManager.getInstance();
      featureToggleManager.enableGenericFeature(FeatureToggleManager.FeatureName.OVERLAY_DETECTION, isOverlayDetectionEnabled);
      new VGuardFactory().getVGuard(reactContext.getCurrentActivity(), builder);
    } catch (Exception e) {
      vgExceptionHandler.handleException(e);
    }
  }

  private void getVGuardInstance() {
    if (vGuardMgr == null) {
      try {
        vGuardMgr = VGuardFactory.getInstance();
        // necessary for VGuard to be informed of the activity's lifecycle
        hook = new ActivityLifecycleHook(vGuardMgr);
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
  }

  private static void sendEventEmitter(ReactApplicationContext reactAppCtx, String eventName, @Nullable Object params) {
    WritableMap emitData = Arguments.createMap();
    emitData.putString("action", eventName);
    String key = "data";
    if(params != null) {
      if(params instanceof Integer) {
        emitData.putInt(key, (Integer) params);
      }
      else if(params instanceof Long) {
        emitData.putString(key, Long.toString((long)params));
      }
      else if(params instanceof String) {
        emitData.putString(key, (String) params);
      }
      else if(params instanceof WritableArray) {
        emitData.putArray(key, (WritableArray) params);
      }
      else if(params instanceof WritableMap) {
        emitData.putMap(key, (WritableMap) params);
        Log.d(TAG, "add params to Map");
      }
    }

    reactAppCtx
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
            .emit(VGUARD_EVENTS, emitData);
  }

  private VGuardBroadcastReceiver initialVGuardReceiver() {
    return new VGuardBroadcastReceiver(null) {
      @Override
      public void onReceive(Context context, Intent intent) {
        super.onReceive(context, intent);

        if (ACTION_FINISH.equals(intent.getAction())) {
          Log.d(TAG, "\nProfile loaded...");
          sendEventEmitter(reactContext, ACTION_FINISH, null);
          quitApp();
        }
        else if (PROFILE_LOADED.equals(intent.getAction())) {
          Log.d(TAG, "\nProfile loaded...");
          sendEventEmitter(reactContext, PROFILE_LOADED, null);
        }
        else if (VOS_READY.equals(intent.getAction())) {
          vosFirmwareCode = intent.getLongExtra(FIRMWARE_RETURN_CODE, 0);
          Log.d(TAG, "\nv-os return code: " + vosFirmwareCode);
          getVGuardInstance();
          if(vGuardMgr != null && !TextUtils.isEmpty(tiUrl)) {
            vGuardMgr.setThreatIntelligenceServerURL(tiUrl);
          }
          sendEventEmitter(reactContext, VOS_READY, vosFirmwareCode);
        }
        else if (ACTION_SCAN_COMPLETE.equals(intent.getAction())) {
          WritableMap resultMap = Arguments.createMap();
          if (intent.hasExtra(VGuardBroadcastReceiver.SCAN_COMPLETE_LEVEL)) {
            VGScanLevel scanLevel = (VGScanLevel)intent.getSerializableExtra(VGuardBroadcastReceiver.SCAN_COMPLETE_LEVEL);
            resultMap.putString(SCAN_COMPLETE_LEVEL, scanLevel + "");
          }
          WritableArray arrayData = getArrayThreats(intent);
          resultMap.putArray(ACTION_SCAN_COMPLETE, arrayData);
          sendEventEmitter(reactContext, ACTION_SCAN_COMPLETE, resultMap);
        }
        else if (VGUARD_OVERLAY_DETECTED.equals(intent.getAction())) {
          sendEventEmitter(reactContext, VGUARD_OVERLAY_DETECTED, null);
        }
        else if (VGUARD_OVERLAY_DETECTED_DISABLE.equals(intent.getAction())) {
          sendEventEmitter(reactContext, VGUARD_OVERLAY_DETECTED_DISABLE, null);
        }
        else if (VGUARD_VIRTUAL_SPACE_DETECTED.equals(intent.getAction())) {
          sendEventEmitter(reactContext, VGUARD_VIRTUAL_SPACE_DETECTED, null);
        }
        else if (VGUARD_STATUS.equals(intent.getAction())) {
          Log.d(TAG, "\nVGuard status ... ");

          // If the profile that you use has the vguardHandleThreatPolicy set to false
          if (intent.hasExtra(VGUARD_HANDLE_THREAT_POLICY)) {
            handleThreatPolicy(intent);
          }
          // If the profile that you use has the sslAlertBypass set to true
          else if (intent.hasExtra(VGUARD_SSL_ERROR_DETECTED)) {
            handleSslErrorDetection(intent, VGUARD_SSL_ERROR_DETECTED);
          } else {
            String message = intent.getStringExtra(VGUARD_MESSAGE);
            sendEventEmitter(reactContext, VGUARD_STATUS, message);
          }
        }
        else if (VGUARD_SCREEN_SHARING_DETECTED.equals(intent.getAction())) {
          StringBuilder builder = new StringBuilder();
          try {
            Log.d(TAG, "\n\nScreen Sharing Detected");
            ArrayList<String> sharingDisplaysList = intent.getStringArrayListExtra(VGuardBroadcastReceiver.VGUARD_SCREEN_SHARING_DISPLAY_NAMES);
            if(sharingDisplaysList != null) {
              for (String sharingDisplay : sharingDisplaysList) {
                builder.append(sharingDisplay).append("\n");
              }
            } else {
              String sharingDisplays = intent.getStringExtra(VGUARD_SCREEN_SHARING_DISPLAY_NAMES);
              JSONArray jsonArray = new JSONArray(sharingDisplays);
              builder.append(jsonArray);
            }
          } catch (Exception e) {
            e.printStackTrace();
          }
          sendEventEmitter(reactContext, VGUARD_SCREEN_SHARING_DETECTED, builder.toString());
        } else if (VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED.equals(intent.getAction())) {
          StringBuilder builder = new StringBuilder();
          builder.append("\nSideLoad Detected");
          try {
            String sideloadlist = intent.getStringExtra(VGUARD_SIDELOADED_RESULT);
            if ( !TextUtils.isEmpty(sideloadlist)) {
              JSONArray jsonArray = new JSONArray(sideloadlist);
              builder.append("\n").append(jsonArray);
            } else {
              String packageID = intent.getStringExtra("vkey.android.vguard.VGUARD_SIDELOADED_PACKAGE_ID");
              String source = intent.getStringExtra("vkey.android.vguard.VGUARD_SIDELOADED_SOURCE");
              builder.append("\nPackageID: ").append(packageID);
              builder.append("\nSource Install: ").append(source);
            }

          } catch (Exception e) {
            e.printStackTrace();
          }
          Log.d(TAG, builder.toString());
          sendEventEmitter(reactContext, VGUARD_SIDELOADED_APP_WITH_ACCESSIBILITY_PERMISSION_DETECTED, builder.toString());
        } else if (VGUARD_DEVELOPER_OPTIONS_ENABLED.equals(intent.getAction())) {
          sendEventEmitter(reactContext, VGUARD_DEVELOPER_OPTIONS_ENABLED, null);
        } else if (VGUARD_AIRDROID_PORT_IS_OPEN.equals(intent.getAction())) {
          sendEventEmitter(reactContext, VGUARD_AIRDROID_PORT_IS_OPEN, null);
        } else if (VGUARD_VIRTUAL_TAP_DETECTED.equals(intent.getAction())) {
          VGVirtualTapType type = (VGVirtualTapType) intent.getSerializableExtra(VGuardBroadcastReceiver.VGUARD_VIRTUAL_TAP_TYPE);
          if(type != null) {
            String virtualTapTypeString = convertVirtualTapTypeToString(type);
            sendEventEmitter(reactContext, VGUARD_VIRTUAL_TAP_DETECTED, virtualTapTypeString);
          }
        } else if (VGUARD_NETWORK_DETECTED.equals(intent.getAction())) {
          VGuardNetworkType[] networkTypes = (VGuardNetworkType[]) intent.getSerializableExtra(VGuardBroadcastReceiver.VGUARD_NETWORK_TYPES);
          if(networkTypes != null) {
            WritableArray arrayData = getArrayNetworkTypes(networkTypes);
            sendEventEmitter(reactContext, VGUARD_NETWORK_DETECTED, arrayData);
          }
        }
      }
    };
  }

  private String convertVirtualTapTypeToString(VGVirtualTapType type) {
    switch (type) {
      case VIRTUAL_TAP: return "VIRTUAL_TAP";
      case CORELLIUM_VD: return "CORELLIUM_VD";
      case ANDROID_STUDIO_EMULATOR: return "ANDROID_STUDIO_EMULATOR";
      case AIRDROID_USB_DEBUG: return "AIRDROID_USB_DEBUG";
      case P_CLOUDY_NON_VIRTUAL: return "P_CLOUDY_NON_VIRTUAL";
      default: return "UNKNOWN";
    }
  }

  private WritableArray getArrayNetworkTypes(VGuardNetworkType[] networkTypes) {
    WritableArray arrayData = Arguments.createArray();
    for(VGuardNetworkType networkType: networkTypes) {
      String networkTypeString = convertNetworkTypeToString(networkType);
      arrayData.pushString(networkTypeString);
    }
    return arrayData;
  }

  private String convertNetworkTypeToString(VGuardNetworkType networkType) {
    switch (networkType) {
      case WIFI: return "WIFI";
      case CELLULAR: return "CELLULAR";
      case VPN: return "VPN";
      case ETHERNET: return "ETHERNET";
      case BLUETOOTH: return "BLUETOOTH";
      case WIFI_AWARE: return "WIFI_AWARE";
      case LOWPAN: return "LOWPAN";
      case USB: return "USB";
      case THREAD: return "THREAD";
      case SATELLITE: return "SATELLITE";
      default: return "UNKNOWN";
    }
  }
  private WritableArray getArrayThreats(Intent intent) {
    ArrayList<Parcelable> detectedThreats = intent.getParcelableArrayListExtra(VGuardBroadcastReceiver.SCAN_COMPLETE_RESULT);
    StringBuilder builder = new StringBuilder();
    WritableArray arrayData = Arguments.createArray();
    if(detectedThreats != null) {
      for (Parcelable info : detectedThreats) {
        BasicThreatInfo threatInfo = (BasicThreatInfo) info;
        WritableMap infoMap = Arguments.createMap();
        infoMap.putString("ThreatClass", threatInfo.getThreatClass());
        infoMap.putString("ThreatInfo", threatInfo.getThreatInfo());
        infoMap.putString("ThreatName", threatInfo.getThreatName());
        infoMap.putString("ThreatPackageID", threatInfo.getThreatPackage());

        arrayData.pushMap(infoMap);

        // print log
        String infoStr = info.toString();
        builder.append(infoStr).append("\n");
      }
    }
    Log.d(TAG, "\n\nAction Scan Complete: " + builder);
    return arrayData;
  }

  private void handleSslErrorDetection(Intent intent, String eventName) {
    boolean sslErr = intent.getBooleanExtra(VGUARD_SSL_ERROR_DETECTED, false);
    Log.i(TAG, "\n\n" + VGUARD_SSL_ERROR_DETECTED + ":" + sslErr);

    WritableMap mapData = Arguments.createMap();
    mapData.putBoolean(VGUARD_SSL_ERROR_DETECTED, sslErr);
    if (sslErr) {
      try {
        String message = intent.getStringExtra(VGUARD_MESSAGE);
        if(message != null) {
          JSONObject jsonObject = new JSONObject(message);
          mapData.putString(VGUARD_ALERT_TITLE, jsonObject.optString(VGUARD_ALERT_TITLE));
          mapData.putString(VGUARD_ALERT_MESSAGE, jsonObject.optString(VGUARD_ALERT_MESSAGE));
          Log.i(TAG, jsonObject.toString());
        }
      } catch (Exception ignored) {
      }
    }

    sendEventEmitter(reactContext, eventName, mapData);
  }

  private void handleThreatPolicy(Intent intent) {
    WritableMap mapData = Arguments.createMap();
    WritableArray responses = getArrayResponses(intent);

    mapData.putArray("responses", responses);

    if (intent.hasExtra(VGuardBroadcastReceiver.SCAN_COMPLETE_LEVEL)) {
      VGScanLevel scanLevel = (VGScanLevel)intent.getSerializableExtra(SCAN_COMPLETE_LEVEL);
      mapData.putString(SCAN_COMPLETE_LEVEL, scanLevel+"");
    }

    long disabledAppExpired = intent.getLongExtra(VGUARD_DISABLED_APP_EXPIRED, 0);
    mapData.putInt(VGUARD_DISABLED_APP_EXPIRED, (int) disabledAppExpired);

    sendEventEmitter(reactContext, VGUARD_HANDLE_THREAT_POLICY, mapData);
  }

  private WritableArray getArrayResponses(Intent intent) {
    ArrayList<VGThreatResponse> threatResponses = intent.getParcelableArrayListExtra(SCAN_COMPLETE_RESULT);
    WritableArray arrayData = Arguments.createArray();
    if(threatResponses != null) {
      for (VGThreatResponse response : threatResponses) {
        WritableMap infoMap = Arguments.createMap();
        infoMap.putString("title", response.getTitle());
        infoMap.putString("message", response.getMessage());
        infoMap.putString("categoryName", response.getCategoryName());
        infoMap.putString("categoryValue", response.getCategoryValue());
        infoMap.putString("threatPolicy", convertThreatPolicyToString(response.getThreatPolicy()));
        infoMap.putString("formattedMessage", response.getFormattedMessage());
        infoMap.putArray("threats", convertListThreatToWritableArray(response.getThreats()));
        arrayData.pushMap(infoMap);
      }
    }
    return arrayData;
  }

  private WritableArray convertListThreatToWritableArray(List<VGThreatAppInfo> threats) {
    WritableArray writableArray = Arguments.createArray();
    for(VGThreatAppInfo threat : threats) {
        WritableMap infoMap = Arguments.createMap();
        infoMap.putString("threatInfo", threat.getThreatInfo());
        infoMap.putString("appName", threat.getAppName());
        infoMap.putString("packageName", threat.getPackageName());
        infoMap.putString("installSource", threat.getInstallSource());
        infoMap.putBoolean("accessibilityEnabled", threat.isAccessibilityEnabled());

        writableArray.pushMap(infoMap);
    }
    return writableArray;
  }

  private String convertThreatPolicyToString(VGThreatPolicy highestResponse) {
    if(highestResponse != null) {
      switch(highestResponse) {
        case BY_PASS: {
          return "BY_PASS";
        }
        case ALERT_USER: {
          return "ALERT_USER";
        }
        case QUIT_APP: {
          return "QUIT_APP";
        }
        case DISABLE_APP: {
          return "DISABLE_APP";
        }
        case BLOCK_NETWORK:{
          return "BLOCK_NETWORK";
        }
      }
    }
    return "";
  }
  /**   React Methods   ***/

  @ReactMethod
  public void requestScan() {
    Log.d(TAG, "requestScan called");
    if(vGuardMgr != null) {
      vGuardMgr.requestScan();
    }
  }

  @ReactMethod
  public void getCustomerID(Promise promise){
    int customerId = ERROR;
    if(vGuardMgr != null) {
      customerId = vGuardMgr.getCustomerId();
    }
    if(promise != null) {
      promise.resolve(Integer.toString(customerId));
    }
  }

  @ReactMethod
  public void getPassword(Promise promise) {
    String password = null;
    if(vGuardMgr != null) {
      byte[] data = vGuardMgr.getPassword();
      password = Utility.encodeHex(data);
    }
    if(promise != null) {
      promise.resolve(password);
    }
  }

  @ReactMethod
  public void lockVos(Promise promise) {
      int result = ERROR;
      if (vGuardMgr != null) {
          result = vGuardMgr.lockVos();
      }
      if (promise != null) {
          promise.resolve(result);
      }
  }

  @ReactMethod
  public void encryptUsingCustomerKey(String plaintext, Promise promise) {
    String result = null;
    if(vGuardMgr != null) {
      byte[] bytes = plaintext.getBytes();
      byte[] encrypt = vGuardMgr.encryptUsingCustomerKey(bytes);
      result = Utility.encodeHex(encrypt);
    }
    if(promise != null) {
        promise.resolve(result);
    }
  }

  @ReactMethod
  public void decryptUsingCustomerKey(String ciphertext, Promise promise) {
     String result = null;
    if(vGuardMgr != null) {
      byte[] bytes = Utility.hexToBytes(ciphertext);
      byte[] descrypt = vGuardMgr.decryptUsingCustomerKey(bytes);
      result = new String(descrypt);
    }
    if(promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void getTroubleshootingId(Promise promise) {
    String troubleshootingId = null;
    if(vGuardMgr != null) {
      troubleshootingId = vGuardMgr.getTroubleshootingId();
    }
    if(promise != null) {
      promise.resolve(troubleshootingId);
    }
  }

  @ReactMethod
  public void getIsVosStarted(Promise promise) {
      boolean result = false;
    if(vGuardMgr != null) {
        result = vGuardMgr.getIsVosStarted();
    }
      if(promise != null) {
          promise.resolve(result);
      }
  }

  @ReactMethod
  public void sdkVersion(Promise promise) {
    String result = "";
    if(vGuardMgr != null) {
      result = vGuardMgr.sdkVersion();
    }
    if(promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void getFirmwareVersion(Promise promise) {
    String result = "";
    if(vGuardMgr != null) {
      result = VosWrapper.getInstance(reactContext).getFirmwareVersion();
    }
    if(promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void getProcessorVersion(Promise promise) {
    String result = "";
    if(vGuardMgr != null) {
      result = VosWrapper.getInstance(reactContext).getProcessorVersion();
    }
    if(promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void isVosRunning(Promise promise) {
      int res = VosWrapper.getInstance(reactContext).execute(new Runnable() {
        @Override
        public void run() {

        }
      });
    Log.i(TAG, "vos.execute: " + res);

    if(promise != null) {
      promise.resolve(res > 0);
    }
  }

  public static String bytesToHex(byte[] data) {
    if (data == null) {
      return null;
    }
    int len = data.length;
    String str = "";
    for (int i = 0; i < len; i++) {
      if ((data[i] & 0xFF) < 16)
        str = str + "0" + java.lang.Integer.toHexString(data[i] & 0xFF);
      else
        str = str + java.lang.Integer.toHexString(data[i] & 0xFF);
    }
    return str;
  }

  @ReactMethod
  public void setVirtualTapDetectionEnabled(boolean virtualTapDetectionEnabled) {
    isVirtualTapDetectionEnabled = virtualTapDetectionEnabled;
  }
  @ReactMethod
  public void setUsePackageManager(boolean usePackageManager) {
    isUsePackageManagerEnabled = usePackageManager;
  }

  @ReactMethod
  public void getIsVGuardStarted(Promise promise) {
    boolean result = VGuardFactory.getIsVGuardStarted();
    if (promise != null) {
      promise.resolve(result);
    }
  }

  @ReactMethod
  public void getSecurityStatus(Promise promise) {
    String result = "";
    if (vGuardMgr != null) {
      VGSecurityStatus status = vGuardMgr.getSecurityStatus();
      result = convertSecurityStatusToString(status);
    }
    if (promise != null) {
      promise.resolve(result);
    }
  }

  private String convertSecurityStatusToString(VGSecurityStatus status) {
    if(status == VGSecurityStatus.SAFE) {
      return "SAFE";
    } else if(status == VGSecurityStatus.UNSAFE) {
      return "UNSAFE";
    } else {
      return "UNDETERMINED";
    }
  }

  private void quitApp() {
    ActivityManager am = (ActivityManager) reactContext.getSystemService(Context.ACTIVITY_SERVICE);
    List<ActivityManager.AppTask> appTaskList = am.getAppTasks();
    if (appTaskList != null && !appTaskList.isEmpty()) {
      ActivityManager.AppTask appTask = appTaskList.get(0);
      appTask.finishAndRemoveTask();
      Handler handler = new Handler();
      handler.postDelayed(() -> {
        try {
          android.os.Process.killProcess(android.os.Process.myPid());
        } catch (Exception ignored) {}
      }, 600);
    }
  }
}