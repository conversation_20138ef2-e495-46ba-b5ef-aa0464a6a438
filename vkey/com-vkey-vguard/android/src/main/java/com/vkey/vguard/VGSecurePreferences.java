package com.vkey.vguard;

import android.content.Context;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.vkey.android.internal.vguard.secure.preference.SecurePreferences;

import javax.annotation.Nonnull;

import static com.vkey.vguard.VGSecurePreferences.VGuardException.RET_MESSAGE_VGUARD_NOT_STARTED;

public class VGSecurePreferences extends ReactContextBaseJavaModule {
    private final String TAG = VGSecurePreferences.class.getName();
    private final ReactApplicationContext reactContext;


    public VGSecurePreferences(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @Nonnull
    @Override
    public String getName() {
        return "VGSecurePreferences";
    }


    /**
     * Returns a stored value from secure preferences
     * @param key
     * @param promise
     */
    @ReactMethod
    public void get(String key, Promise promise) {
        // check whether vos is started or not..
        boolean isVosStarted = VGuardPlugin.isVosStarted();
        String value = null;
        if(isVosStarted) {
            Context context = reactContext;
            SecurePreferences sp = SecurePreferences.getInstance(context);
            try {
                value = sp.get(key);
                promise.resolve(value);
            }
            catch(Exception e) {
                promise.reject(e);
            }
        }
        else {
            promise.reject(new VGuardException(RET_MESSAGE_VGUARD_NOT_STARTED));
        }
    }

    /**
     * Saves key, value in secure preferences
     * @param key
     * @param value
     * @param promise
     */
    @ReactMethod
    public void put(String key, String value, Promise promise) {
        // check whether vos is started or not..
        boolean isVosStarted = VGuardPlugin.isVosStarted();
        if(isVosStarted) {
            Context context = reactContext;
            SecurePreferences sp = SecurePreferences.getInstance(context);
            try {
                sp.put(key, value);
                promise.resolve(true);

            }
            catch(Exception e) {
                promise.reject(e);
            }
        }
        else {
            promise.reject(new VGuardException(RET_MESSAGE_VGUARD_NOT_STARTED));
        }
    }


    /**
     * Removes a key, value pair from secure preferences
     * @param key
     * @param promise
     */
    @ReactMethod
    public void remove(String key, Promise promise) {
        // check whether vos is started or not..
        boolean isVosStarted = VGuardPlugin.isVosStarted();
        if(isVosStarted) {
            Context context = reactContext;
            SecurePreferences sp = SecurePreferences.getInstance(context);
            try {
                sp.remove(key);
                promise.resolve(true);
            }
            catch(Exception e) {
                promise.reject(e);
            }
        }
        else {
            promise.reject(new VGuardException(RET_MESSAGE_VGUARD_NOT_STARTED));
        }
    }

    /**
     * Finds whether a particular key exists in secure preferences
     * @param promise
     * @param key
     */
    @ReactMethod
    public void containskey(String key, Promise promise) {
        // check whether vos is started or not..
        boolean isVosStarted = VGuardPlugin.isVosStarted();
        String value = null;

        if(isVosStarted) {
            Context context = reactContext;
            try {
                SecurePreferences sp = SecurePreferences.getInstance(context);
                boolean contains = sp.containsKey(key);
                value = ((contains)? "1" : "0" );
                promise.resolve(contains);
            }
            catch(Exception e) {
                promise.reject(e);
            }
        }
        else {
            promise.reject(new VGuardException(RET_MESSAGE_VGUARD_NOT_STARTED));
        }
    }

    /**
     * Removes all key, value pairs from secure preferences
     * @param promise
     */
    @ReactMethod
    public void clear(Promise promise) {
        // check whether vos is started or not..
        boolean isVosStarted = VGuardPlugin.isVosStarted();
        if(isVosStarted) {
            Context context = reactContext;
            SecurePreferences sp = SecurePreferences.getInstance(context);
            try {
                sp.clear();
                promise.resolve(true);
            }
            catch(Exception e) {
                promise.reject(e);
            }
        }
        else {
            promise.reject(new VGuardException(RET_MESSAGE_VGUARD_NOT_STARTED));
        }
    }

    static class VGuardException extends Exception {
        static final String RET_MESSAGE_VGUARD_NOT_STARTED = "vguard is not started..";
        public VGuardException(String errorMessage) {
            super(errorMessage);
        }
    }
}
