package com.vkey.vguard.keyboard;


import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.uimanager.events.Event;
import com.facebook.react.uimanager.events.RCTEventEmitter;

/**
 * Event emitted by EditText native view when key pressed
 */
public class ReactTextInputKeyPressEvent extends Event<ReactTextInputKeyPressEvent> {

    public static final String EVENT_NAME = "topKeyPress";

    private final String mKey;

    public ReactTextInputKeyPressEvent(int viewId, String key) {
        super(viewId);
        this.mKey = key;
    }

    @Override
    public String getEventName() {
        return EVENT_NAME;
    }

    @Override
    public boolean canCoalesce() {
        return false; // Key presses should not be coalesced
    }

    @Override
    public void dispatch(RCTEventEmitter rctEventEmitter) {
        rctEventEmitter.receiveEvent(getViewTag(), getEventName(), serializeEventData());
    }

    private WritableMap serializeEventData() {
        WritableMap eventData = Arguments.createMap();
        eventData.putString("key", mKey);
        return eventData;
    }
}

