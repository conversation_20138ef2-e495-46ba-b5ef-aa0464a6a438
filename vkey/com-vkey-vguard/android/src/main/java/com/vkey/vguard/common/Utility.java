package com.vkey.vguard.common;

import androidx.annotation.Nullable;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.WritableNativeArray;
import com.facebook.react.modules.core.DeviceEventManagerModule;

import java.util.ArrayList;
import java.util.List;

public class Utility {
    private static final char[] hexArray = "0123456789abcdef".toCharArray();
    private static char[] encodeHexChar(byte[] data) {
        char[] hexChars = new char[data.length * 2];
        int v;
        for (int j = 0; j < data.length; j++) {
            v = data[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return hexChars;
    }

    public static String encodeHex(byte[] data) {
        if(data != null) {
            char[] enHex = encodeHexChar(data);
            return new String(enHex);
        }
        return null;
    }

    public static String bytesToHex(byte[] data) {
        if (data == null) {
            return null;
        }

        int len = data.length;
        String str = "";
        for (int i = 0; i < len; i++) {
            if ((data[i] & 0xFF) < 16)
                str = str + "0" + Integer.toHexString(data[i] & 0xFF);
            else
                str = str + Integer.toHexString(data[i] & 0xFF);
        }
        return str;
    }

    public static byte[] hexToBytes(final String strHex) {
        if(strHex == null) return null;
        char[] hex = strHex.toCharArray();
        int length = hex.length / 2;
        byte[] raw = new byte[length];
        for (int i = 0; i < length; i++) {
            int high = Character.digit(hex[i * 2], 16);
            int low = Character.digit(hex[i * 2 + 1], 16);
            int value = (high << 4) | low;
            if (value > 127)
                value -= 256;
            raw[i] = (byte) value;
        }
        return raw;
    }

    public static void sendEvent(ReactApplicationContext reactAppCtx, String eventName, @Nullable Object params) {
        reactAppCtx
                .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter.class)
                .emit(eventName, params);
    }


    public static WritableArray byteArrayToBoolReadableArray(byte[] arr) {
        WritableArray writableArray = new WritableNativeArray();
        byte[] pos = new byte[]{(byte)0x80, 0x40, 0x20, 0x10, 0x8, 0x4, 0x2, 0x1};

        for(int i = 0; i < arr.length; i++){
            for(int k = 0; k < 8; k++){
                boolean res = (arr[i] & pos[k]) != 0;
                writableArray.pushBoolean(res);
            }
        }

        return writableArray;
    }

    public static byte[] readableArrayToByteBoolArray(ReadableArray readableArray) {
        byte[] bytesArr = new byte[readableArray.size() / 8 + 1];
        for (int entry = 0; entry < bytesArr.length; entry++) {
            for (int bit = 0; bit < 8; bit++) {
                if (readableArray.getBoolean(entry * 8 + bit)) {
                    bytesArr[entry] |= (128 >> bit);
                }
            }
        }

        return bytesArr;
    }

    public static WritableArray byteArrayToStringWritableArray(byte[] arr) {

        WritableArray writableArray = new WritableNativeArray();
        byte[] pos = new byte[]{(byte)0x80, 0x40, 0x20, 0x10, 0x8, 0x4, 0x2, 0x1};

        for(int i = 0; i < arr.length; i++){
            for(int k = 0; k < 8; k++){
                boolean res = (arr[i] & pos[k]) != 0;
                writableArray.pushBoolean(res);
            }
        }

        return writableArray;
    }

    public static byte[] readableArrayToByteStringArray(ReadableArray readableArray) {
        List<Byte> bytes = new ArrayList<>(readableArray.size() * 5);
        for (int i = 0; i < readableArray.size(); i++) {
            for (byte b :readableArray.getString(i).getBytes()) {
                bytes.add(b);
            }
        }

        byte[] bytesArr = new byte[bytes.size()];
        for (int i = 0; i < bytes.size(); i++) {
            bytesArr[i] = bytes.get(i);
        }

        return bytesArr;
    }

}
