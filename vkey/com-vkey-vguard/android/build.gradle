apply plugin: 'com.android.library'

android {
    compileSdkVersion 35
    defaultConfig {
        minSdkVersion 21
        targetSdkVersion 35
        versionCode 2
        versionName "4.10.4"
    }
    lintOptions {
        abortOnError false
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    buildFeatures {
        viewBinding true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'com.facebook.react:react-native:+'

    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])

    // VKey SDK
    api 'vos.app.protection:vosAppProtection:4.10'
    api 'vos.processor:vosProcessor:4.10'

    // VKey SDK dependecies
    implementation 'com.getkeepsafe.relinker:relinker:1.4.4'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.lifecycle:lifecycle-process:2.7.0'

    implementation 'com.google.code.gson:gson:2.8.6'
    api 'io.jsonwebtoken:jjwt-api:0.10.7'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.10.7'
    runtimeOnly('io.jsonwebtoken:jjwt-orgjson:0.10.7') {
        exclude group: 'org.json', module: 'json' //provided by Android natively
    }
}
  