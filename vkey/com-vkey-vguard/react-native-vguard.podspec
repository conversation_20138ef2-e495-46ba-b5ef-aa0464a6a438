require "json"

package = JSON.parse(File.read(File.join(__dir__, "package.json")))

Pod::Spec.new do |s|
  s.name         = "react-native-vguard"
  s.version      = package["version"]
  s.summary      = package["description"]
  s.description  = <<-DESC
                  react-native-vguard
                   DESC
  s.homepage     = "https://github.com/github_account/react-native-vguard"
  s.license      = "MIT"
  # s.license    = { :type => "MIT", :file => "FILE_LICENSE" }
  s.authors      = { "Your Name" => "<EMAIL>" }
  s.platforms    = { :ios => "9.0" }
  s.source       = { :git => "https://github.com/github_account/react-native-vguard.git", :tag => "#{s.version}" }

  s.source_files = "ios/*.{h,m}"
  s.requires_arc = true
  s.frameworks  = "UIKit", "CoreGraphics", "Foundation", "MobileCoreServices", "Security", "SystemConfiguration"
  s.libraries = "sqlite3", "z", "c++"
  s.compiler_flags = '-ObjC'

  s.vendored_frameworks = 'ios/*.xcframework'
  s.public_header_files = 'ios/*.h'
  s.static_framework = true
  
  s.dependency "React"
  # ...
  # s.dependency "..."
end

