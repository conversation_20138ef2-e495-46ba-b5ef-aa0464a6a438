const TextInputState = {
    /**
     * Internal state
     */
    _currentlyFocusedNode: (null),
  
    /**
     * Returns the ID of the currently focused text field, if one exists
     * If no text field is focused it returns null
     */
    currentlyFocusedField() {
      try{
        if (document != undefined && document.activeElement !== this._currentlyFocusedNode) {
          this._currentlyFocusedNode = null;
        }
      }
      catch(e) {}
      return this._currentlyFocusedNode;
    },
  
    /**
     * @param {Object} TextInputID id of the text field to focus
     * Focuses the specified text field
     * noop if the text field was already focused
     */
    focusTextInput(textFieldNode) {
      if (textFieldNode !== null) {
        this._currentlyFocusedNode = textFieldNode;
        if (document != null && document.activeElement !== textFieldNode) {
          UIManager.focus(textFieldNode);
        }
      }
    },
  
    /**
     * @param {Object} textFieldNode id of the text field to focus
     * Unfocuses the specified text field
     * noop if it wasn't focused
     */
    blurTextInput(textFieldNode) {
      if (textFieldNode !== null) {
        this._currentlyFocusedNode = null;
        if (document != null && document.activeElement === textFieldNode) {
          UIManager.blur(textFieldNode);
        }
      }
    }
  };
  
  export default TextInputState;