// import {requireNativeComponent} from 'react-native';
// module.exports = requireNativeComponent('VKSecureEditText');

import React, { Component } from 'react';
import {
  requireNativeComponent,
  Platform,
  UIManager,
  Text,
  TouchableWithoutFeedback,
  TextInput
} from 'react-native';
import TextInputState from './lib/TextInputState';
import invariant from './lib/invariant';
import { NativeEventEmitter } from 'react-native';


// import {TextPropTypes} from 'deprecated-react-native-prop-types';
import PropTypes from 'prop-types';
// var createReactClass = require('create-react-class');

if (Platform.OS === 'android') {
  // Bamlab: This is the only changed line.
  var VKSecureEditText = requireNativeComponent('VKSecureEditText', null);
  // ---
}

/**
 * Composes `View`.
 *
 * - src: string
 * - borderRadius: number
 * - resizeMode: 'cover' | 'contain' | 'stretch'
 */

// 1. Define the Context
const FocusContext = React.createContext({
  onFocusRequested: () => {},
  focusEmitter: new NativeEventEmitter()
});

// 2. Create a provider component (optional, or inline in your app)
export const FocusContextProvider = ({ children }) => {
  const focusEmitter = new EventEmitter();

  const onFocusRequested = () => {
    console.log('Focus requested');
  };

  return (
    <FocusContext.Provider value={{ onFocusRequested, focusEmitter }}>
      {children}
    </FocusContext.Provider>
  );
};

export default class ReactVKSecureEditText extends Component {
  static contextType = FocusContext;

  static State = TextInputState;

  constructor(props) {
    super(props);
    this.inputRef = React.createRef();
    this._focusSubscription = null;
  }

  isFocused() {
    try {
      return TextInputState.currentlyFocusedField() === React.findNodeHandle(this.inputRef.current);
    } catch (e) {
      return false;
    }
  }

  _onChange = (event) => {
    if (!this.props.onChangeMessage) {
      return;
    }
    this.props.onChangeMessage(event.nativeEvent.message);
  };

  componentDidMount() {
    if (!this.context.focusEmitter) {
      if (this.props.autoFocus) {
        setTimeout(() => { this.focus(); }, 500);
      }
      return;
    }

    this._focusSubscription = this.context.focusEmitter.addListener('focus', (el) => {
      if (this === el) {
        this.requestAnimationFrame(this.focus());
      } else if (this.isFocused()) {
        this.blur();
      }
    });

    if (this.props.autoFocus) {
      this.context.onFocusRequested(this);
    }
  }

  componentWillUnmount() {
    if (this._focusSubscription) {
      this._focusSubscription.remove();
    }
    if (this.isFocused()) {
      this.blur();
    }
  }

  clear = () => {
    this.inputRef.current.setNativeProps({ text: '' });
  };

  _getText = () => {
    return typeof this.props.value === 'string' ? this.props.value : this.props.defaultValue;
  };

  _renderIOS() {
    return (
      <TextInput {...this.props} ref={this.inputRef} />
    );
  }

  _renderAndroid() {
    const { children, selectionState, onSelectionChange, ...otherProps } = this.props;

    let onSelectionChangeHandler;
    if (selectionState || onSelectionChange) {
      onSelectionChangeHandler = (event) => {
        if (selectionState) {
          const { selection } = event.nativeEvent;
          selectionState.update(selection.start, selection.end);
        }
        if (onSelectionChange) {
          onSelectionChange(event);
        }
      };
    }

    const autoCapitalize =
      UIManager.getViewManagerConfig('VKSecureEditText').Constants.AutoCapitalizationType[this.props.autoCapitalize];

    const childCount = React.Children.count(children);

    if (childCount > 1) {
      children = <Text>{children}</Text>;
    }

    return (
      <TouchableWithoutFeedback
        onPress={this._onPress}
        accessible={this.props.accessible}
        accessibilityLabel={this.props.accessibilityLabel}
        accessibilityComponentType={this.props.accessibilityComponentType}
        testID={this.props.testID}>
        <VKSecureEditText
          ref={this.inputRef}
          autoFocus={this.props.autoFocus}
          style={this.props.style}
          autoCapitalize={autoCapitalize}
          autoCorrect={this.props.autoCorrect}
          keyboardType={this.props.keyboardType}
          mostRecentEventCount={0}
          multiline={this.props.multiline}
          numberOfLines={this.props.numberOfLines}
          maxLength={this.props.maxLength}
          onFocus={this._onFocus}
          onBlur={this._onBlur}
          onChange={this._onChange}
          onSelectionChange={onSelectionChangeHandler}
          onTextInput={this._onTextInput}
          onEndEditing={this.props.onEndEditing}
          onSubmitEditing={this.props.onSubmitEditing}
          onLayout={this.props.onLayout}
          password={this.props.password || this.props.secureTextEntry}
          placeholder={this.props.placeholder}
          placeholderTextColor={this.props.placeholderTextColor}
          selectionColor={this.props.selectionColor}
          text={this._getText()}
          underlineColorAndroid={this.props.underlineColorAndroid}
          children={children}
          editable={this.props.editable}
          randomized = {this.props.randomized}
          importantForAutofill = {this.props.importantForAutofill}
          blurOnSubmit = {this.props.blurOnSubmit}
          cursorColor = {this.props.cursorColor}
          caretHidden = {this.props.caretHidden}
          contextMenuHidden = {this.props.contextMenuHidden}
          selectTextOnFocus = {this.props.selectTextOnFocus}
          autoComplete = {this.props.autoComplete}
          returnKeyType = {this.props.returnKeyType}
          disableFullscreenUI = {this.props.disableFullscreenUI}
          returnKeyLabel = {this.props.returnKeyLabel}
          inDialog = {this.props.inDialog}
          borderStyle = {this.props.borderStyle}
        />
      </TouchableWithoutFeedback>
    );
  }

  _onFocus = (event) => {
    if (this.props.onFocus) {
      this.props.onFocus(event);
    }

    if (this.props.selectionState) {
      this.props.selectionState.focus();
    }
  };

  _onPress = (event) => {
    if (this.props.editable || this.props.editable === undefined) {
      this.focus();
    }
  };

  focus = () => {
    if (this.inputRef.current) {
      this.inputRef.current.focus();
    }
  };

  _onBlur = (event) => {
    this.blur();
    if (this.props.onBlur) {
      this.props.onBlur(event);
    }

    if (this.props.selectionState) {
      this.props.selectionState.blur();
    }
  };

  blur = () => {
    if (this.inputRef.current) {
      this.inputRef.current.blur();
    }
  };

  _onTextInput = (event) => {
    if (this.props.onTextInput) {
      this.props.onTextInput(event);
    }
  }

  render() {
    if (Platform.OS === 'ios') {
      return this._renderIOS();
    } else if (Platform.OS === 'android') {
      return this._renderAndroid();
    }
  }
}

// module.exports = requireNativeComponent('VKSecureEditText');
// module.exports = ReactVKSecureEditText;
