REACT-NATIVE VGuard Plugin
==============================

## Contents
- [Introduction](#introduction)
- [Supported platforms](#supported-platforms)
- [Integration](#integration)
- [Getting Started](#getting-started)
- [Code and APK Obfuscations for Android](#code-and-apk-obfuscations-for-android)
  - [Using ProGuard](#using-proguard)
  - [Using DexGuard](#using-dexguard)
- [Manual Installation](#manual-installation)
  - [Android](#android)
  - [iOS](#ios)
- [VGuard Integration](#vguard-integration)
- [Secure Keyboard](#secure-keyboard)
  - [Usage](#usage)
- [SSL Implementation](#ssl-implementation)
- [Secure RESTful APIs](#secure-restful-apis)

## Introduction
V-OS App Protection is an app protection solution built on the V-OS virtual secure element. The V-OS App Protection solution is able to secure the host app against advanced persistent threats, trojans, and rootkits. It offers threat intelligence capability which ensures the integrity of the device, file system, app, keyboard, and network. It also supports real-time threats detection and over-the-air (OTA) update of security policy and firmware to ensure the protection of the app.

## Supported platforms

- Android
- iOS

## Integration
1. Get the assets which contains `signature, firmware, vkeylicensepack, and voscodedesign.vky`.
> Note: iOS doesn't need to add the voscodedesign.vky file.
2. Get the latest version of SDK `(vos-app-protection-android-XXXX-Debug.aar , vos- processor-android-XXXX-Debug.aar)`.
3. Download the profile from `V-OS App Protection Server` dashboard.
4. Get the React Native app protection plugin ( com.vkey.vguard ).
> Notes: If you are using `Threat Intelligence`, make sure you have these assets below: 
> - *manifest.json*
> - *sig_cfs*
5. If you are using TLA, make sure you have the asset below: `tla_enc.cer`.
6. **For Android only:**
   - Get the latest version of cuckoo filter jar file `( cuckoofilter4j-XXX )`.
   - Make sure that you have the java keystore for development/debugging.

## Getting Started
> Do the following steps to install V-OS App Protection to your React Native app project

1. Extract the SDK package `com-vkey-vguard.zip`.
2. In the root directory of your app project, create a vkey.
3. Copy the `com-vkey-vguard` folder extracted in Step 1 and paste it in the vkey.
4. Open the `package.json` file of the React project.
5. Add the following line to the dependencies block:
    ```js
    "dependencies": { 
        "react-native-vguard": "file: PATH/com-vkey-vguard",
        ....
    }
    ```
6. Save the `.json` file and run the following command to install the V-OS App Protection SDK:
    ```
    $ npm install
    ```

## Code and APK Obfuscations for Android

> Note: The source codes of all V-Key SDKs are obfuscated before they are built and released.

### Using ProGuard
If you enabled ProGuard to shrink, obfuscate, and optimize your app, you should add the following wildcard rules to the ProGuard rules file to prevent ProGuard from renaming/removing/obfuscating the codes from V-Key and other required third-party modules. Errors may occur if the codes of V-Key components and other required third-party modules are incorrectly modified/obfuscated.

> Note: V-OS only supports security checking on classes with a package name that has at least 2-level depths. If you want to use -repackageclasses feature of ProGuard, you shall arrange your class and make it with at least 2-level in depth, e.g., -repackageclasses 'one.two'.<br/>
> Note: If you are using ProGuard with the minifyEnable property set to true, you need to make sure that you also use the cuckoofilter4j-1.0.2-customized.jar library supplied by V-Key. Otherwise, you may encounter the duplicate class issue if your app also has certain libraries such as the Guava library which contains duplicate classes.<br/>
> Note: You must exclude org.apache.http library from obfuscation. Otherwise, V-OS App Protection thread scanning will not start.

```
# For VKey
-keep class vkey.** { *; }
-keep class com.vkey.** { *; }
-keep interface vkey.** { *; }
-keep interface com.vkey.** { *; }

-keep class androidx.core.app.VKJobIntentService { *; }
-keep class androidx.core.app.JobIntentService { *; }

# For org.apache.http
-keep class org.apache.http.** { *; }
-dontwarn org.apache.http.**

# For io.jsonwebtoken
-keepattributes InnerClasses

-keep class io.jsonwebtoken.** { *; }
-keepnames class io.jsonwebtoken.* { *; }
-keepnames interface io.jsonwebtoken.* { *; }

-keep class org.bouncycastle.** { *; }
-keepnames class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**

# For cuckoofilter4j
-keep class com.github.mgunlogson.cuckoofilter4j.** { *; }
-keepnames class com.github.mgunlogson.cuckoofilter4j.* { *; }
-keepnames interface com.github.mgunlogson.cuckoofilter4j.* { *; }
```

### Using DexGuard
If you use DexGuard to protect and optimize your app codes, you should add the following wildcard rules to the DexGuard rules file to prevent DexGuard from renaming/removing/obfuscating the codes from V-Key. Errors may occur if the codes of V-Key components are incorrectly modified/encrypted/obfuscated.
> Note: Rules for libsecurefileio.so are required by V-OS SecureFileIO SDK. Rules for libtaInterface.so are required by V-OS CryptoTA SDK. <br/>
> Note: You must exclude org.apache.http library from obfuscation. Otherwise, V-OS App Protection thread scanning will not start.

```
-keep class vkey.** { *; }
-keep class com.vkey.** { *; }
-keep interface vkey.** { *; }
-keep interface com.vkey.** { *; }
-keep class androidx.core.app.VKJobIntentService { *; }
-keep class androidx.core.app.JobIntentService { *; }
-keep class org.apache.http.** { *; }
-dontwarn org.apache.http.**

-keepresourcefiles lib/**/libvosWrapperEx.so
-keepresourcefiles lib/**/libchecks.so
-keepresourcefiles lib/**/libsecurefileio.so
-keepresourcefiles lib/**/libtaInterface.so

-adaptresourcefilecontents !lib/**/libvosWrapperEx.so
-adaptresourcefilecontents !lib/**/libchecks.so
-adaptresourcefilecontents !lib/**/libsecurefileio.so
-adaptresourcefilecontents !lib/**/libtaInterface.so

-keepclasseswithmembernames,includedescriptorclasses class vkey.** {
    native <methods>;
}
-keepclasseswithmembernames,includedescriptorclasses class com.vkey.** {
    native <methods>;
}
```

## Manual Installation

### Android

1. Open project by Android Studio
   - Go to `../vkey/com-vkey-vguard/android/` path.
   - Copy `vos-app-protection-android-xxxx.aar` file to `vosAppProtection` and `vos-processor-android-xxxx.aar` to `vosProcessor` inside the project `:react-native-vguard`
   - Update the version in `artifacts.add("default", file('vos-app-protection-android-xxx.aar'))` inside `vosAppProtection/build.gradle` according to the version you copied to the `vosAppProtection` folder.
   - Update the version in `artifacts.add("default", file('vos-processor-android-xxx.aar'))` inside `vosProcessor/build.gradle` according to the version you copied to the `vosAppProtection` folder.

2. Open `settings.gradle` file from your application.
   - Add two lines to the file.
    ```java
    includeBuild("../vkey/com-vkey-vguard/android/vosAppProtection")
    includeBuild("../vkey/com-vkey-vguard/android/vosProcessor")
    ```

3. Copy the `firmware, signature, vkeylicensepack, voscodesign.vky, and profile` asset files, provided in the package, into the `android/app/src/main/assets/` folder of your app project.
> Note: Please copy TLA ( tla_enc.cer ) and TI ( manifest.json , sig_cfs ) related assets if you are using those features.

4. Add the following lines to `build.gradle` file in the app folder:
    ```
    buildTypes {
      release {
        minifyEnabled true
        proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        signingConfig signingConfigs.release
        crunchPngs false
      }
      debug {
        signingConfig signingConfigs.release
      }
    }
    ```

5. Insert the following lines inside the dependencies block in `android/app/build.gradle`:
   - In dependencies {...} 
   ```java
   implementation project(':react-native-vguard')
   implementation files('libs/cuckoofilter4j-xxx.jar')
   implementation("com.google.guava:guava:32.1.2-android”)
   ```

6. Open up `android/app/src/main/java/[...]/MainActivity.java`
   - Add `import java.lang.ref.WeakReference;` to the imports at the top of the file.
   - Add `VGuardPlugin.setCurrentActivity(this);` to `onCreate` method.

7. Open up `android/app/src/main/java/[...]/MainApplication.java`
   - Add `import com.vkey.vguard.VGuardPlugin;` to the imports at the top of the file.
   - Add `VGuardPlugin.setVGActivityLifecycleCallbacks(this);` into `onCreate` method.

8. Configure permissions and vkey configs, open `android\src\app\AndroidManifest.xml` file
   - Add permissions:
   ```html
   <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
   <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
   <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
   <uses-permission android:name="android.permission.INTERNET" />
   <uses-permission android:name="android.permission.GET_TASKS" />
   <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
   <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
   <uses-permission android:name="android.permission.WAKE_LOCK" />

   <queries>
   	<intent>
   		<action android:name="android.intent.action.MAIN" />
   	</intent>
   </queries>
   ```

   - Add this configurations to the `<application>` tag:
   ```html
   <application
     android:name=".MainApplication"
     android:label="@string/app_name"
     android:icon="@mipmap/ic_launcher"
     android:roundIcon="@mipmap/ic_launcher_round"
     android:allowBackup="false"
     android:supportsRtl="true"
     android:requestLegacyExternalStorage="true"
     android:extractNativeLibs="true"
     android:zygotePreloadName="vkey.android.vos.AppZygote"
     android:theme="@style/AppTheme">
   ```

   - Add these line to `<application>` tag:
   ```html
   <activity
   	android:name="com.vkey.android.support.permission.VGuardPermissionActivity"
   	android:theme="@android:style/Theme.Translucent.NoTitleBar"
   	tools:replace="android:theme"/>

   <service android:name="com.vkey.android.secure.overlay.OverlayService" />

   <service
   	android:name="vkey.android.vos.MgService"
   	android:enabled="true"
   	android:process=":vkey"
   	android:isolatedProcess="true" />

   <service
   	android:name="com.vkey.android.internal.vguard.cache.ProcessHttpRequestIntentService"
   	android:permission="android.permission.BIND_JOB_SERVICE" />

   <meta-data
     android:name="android.max_aspect"
     android:value="2.1" />

   <uses-library
     android:name="org.apache.http.legacy"
     android:required="false" />
   ```

### iOS
1. Copy “vos-app-protection-ios-xxx.tgz”, “vos-processor-ios-xxx.tgz” file to `com-vkey-vguard/ios/` then unzip here first. After unzipping the tgz files, we will have both `VGuard.xcframework` and `VosWrapper.xcframework`.
2. Copy `firmware, signature, vkeylicensepack, and profile` asset files and add them to your iOS folder:
   - Open your `***OrganizationName***.xcworkspace` to add the assets.
   - Move to `Build Phases` >> `Copy Bundle Resources` and add asset files.
3. Go to the React Native App Project root folder and install the Pod with the command:
   - `cd ios && pod install && cd..`

## VGuard Integration

```js
import { VGuardPlugin } from 'react-native-vguard'

useEffect(() => {
  startVGuard();
    DeviceEventEmitter.addListener(VGuardPlugin.VGUARD_EVENTS, onVguardEvents);
  return (() => { 	
    DeviceEventEmitter.removeAllListeners(VGuardPlugin.VGUARD_EVENTS);
  });
});

const startVGuard = async () => {
  console.log('START VGUARD');
  // Set url of synchronizing the vos logs if enabled 
  const tlaUrl = "https://stg-cloud.v-key.com";
  if (tlaUrl) {
    VGuardPlugin.setLoggerBaseUrl(tlaUrl);
  }
  // Set TI Url if enabled 
  const tiUrl = "https://stg-cloud.v-key.com/";
  if (tiUrl) {
    VGuardPlugin.setThreatIntelligenceServerURL(tiUrl);
  }
  // 0: DEFAULT, 1: HIGH
  VGuardPlugin.setMemoryConfiguration(1);
  // Enable the overlay detection
  if(Platform.OS === 'android'){
    VGuardPlugin.setOverlayDetectionEnabled(false);
  }
  // SSL Pinning is enabled; set true to disable it.
  VGuardPlugin.allowsArbitraryNetworking(false);
  // Intialize Vguard 
  VGuardPlugin.setupVGuard();
}

const = onVguardEvents = event => {
  const action = event.action
  console.log(". event.action: " + event.action)

  if(action == VGuardPlugin.VGUARD_PROFILE_LOADED) {
    console.log("PROFILE_LOADED");
  }
  else if(action == VGuardPlugin.ACTION_SCAN_COMPLETE) {
    const mapData = event.data;
      var scanLevel = mapData[VGuardPlugin.SCAN_COMPLETE_LEVEL]
      var threats = mapData[VGuardPlugin.ACTION_SCAN_COMPLETE]
      if(scanLevel != null) {
        printLogs("Scan level:" + scanLevel)
      }
      if (threats != null && threats.length > 0) {
        for (let i = 0; i < threats.length; i++) {
          var threatInfo = threats[i];
          printLogs(
            'Threat Info: ' +
              threatInfo.ThreatClass +
              ' - ' +
              threatInfo.ThreatName +
              ' - ' +
              threatInfo.ThreatInfo +
              ' - ' +
              threatInfo.ThreatPackageID,
          );
        }
      } else {
        printLogs('Scan complete, no threats found!');
      }
  }
  else if (action == VGuardPlugin.VGUARD_PROFILE_LOADED) {
    console.log("RN: Profile loaded"); 
    // Set TI Url if enabled   
    const tiUrl = ""
    if (tiUrl) { 
      VGuardPlugin.setThreatIntelligenceServerURL(tiUrl)   
    } 
  }
  else if(action == VGuardPlugin.VOS_READY) {
    console.log("v-os return code: " + event.data);
  }
  else if(action == VGuardPlugin.VGUARD_ERROR) {
    const errorCode = event.data;
    if(errorCode == "-1039" || errorCode == "20050") {
        console.log("Emulator is detected: " + errorCode);
    } else {
        console.log("Initialize V-Guard is failure caused: " + errorCode);
    }
  }
  else if(action == VGuardPlugin.VGUARD_SSL_ERROR_DETECTED) {
    const data = event.data
    console.log("alertTitle: " + data.alertTitle);
    console.log("alertMessage: " + data.alertMessage);
  }
  // Below events are for Android only
  else if(action == VGuardPlugin.RESET_VOS_STORAGE) {
    console.log("App has been reset VOS storage, kill app and then run again.");
  }
  else if(action == VGuardPlugin.VGUARD_VIRTUAL_SPACE_DETECTED) {
    console.log("app is running on Virtual Space");
  }
  else if(action == VGuardPlugin.VGUARD_OVERLAY_DETECTED_DISABLE) {
    console.log("OVERLAY DETECTED is DISABLE");
  }
  else if(action == VGuardPlugin.VGUARD_OVERLAY_DETECTED) {
    console.log("OVERLAY DETECTED!");
    // this.showAlert("An overlay view was detected!");
  }
  else if(action == VGuardPlugin.VGUARD_SCREEN_SHARING_DETECTED) {
    console.log("VGUARD_SCREEN_SHARING_DETECTED");
    const data = event.data
    if(data != null && data.length > 0) {
      for (let i=0; i < data.length; i++) {
        console.log("Screen Name: " + data[i]);
      }
    }
  }
  else if(action == VGuardPlugin.VGUARD_HANDLE_THREAT_POLICY) {
    const data = event.data;
      const scanLevel = data[VGuardPlugin.SCAN_COMPLETE_LEVEL]
      if(scanLevel != null) {
        printLogs("Scan level in Vguard Handle Threat Policy: " + scanLevel)
      }
      const responses = data.responses;
      for (const response of responses) {
        const threats = response.threats
        if (threats != null && threats.length > 0) {
          for (let i = 0; i < threats.length; i++) {
            var threatInfo = threats[i];
            printLogs(
              'Threat Info: ' +
              threatInfo.threatInfo +
              ' - ' +
              'appName: ' + threatInfo.appName +
              ' - ' +
              'packageName:' +threatInfo.packageName +
              ' - ' +
              'installSource:' + threatInfo.installSource +
              ' - ' +
              'accessibilityEnabled: '+ threatInfo.accessibilityEnabled
            );
          }
        }
      }
  }  
  else {
    console.log("message: " + event.data);
  }
}
```

## Secure Keyboard

### Usage

> Notes: SDK supports multiline for the Android platform only.
> 
> **For iOS**:
> - You need to add [the section 2](#ios-2) on the iOS specifially.
> - You can use ReactVKSecureEditText or TextInput to implement the SecureKeyboard.
---

1. Add these codes below to the `render`:
    ```js
    import {ReactVKSecureEditText} from 'react-native-vguard'

    // Add this to the top of the file
    const [plainText, setPlainText] = useState('');

    // render()
    <ReactVKSecureEditText 
    placeholder="6-20 Char plain text"
    randomized = {true}
    value={plainText}
    keyboardType = 'numeric'
    onChangeText={(txt) => {setPlainText(txt)}} />
    ```

2. <span id='ios-2'>Add these codes to implement the *SecureKeyBoard* on the iOS platform.</span>
    ```js
    import {VKSecureKeypadPlugin} from 'react-native-vguard'

    const setupSecureKeyboard = () => {
      // Enable SecureKeyboard
      VKSecureKeypadPlugin.setEnableKeyboard(true)
      // Scramble Keypad for Numeric Keyboard type
      VKSecureKeypadPlugin.setEnableScrambleKeypad(true);

      // Customize SecureKeyboard Keyboard UI
      VKSecureKeypadPlugin.setKeyboardBackgroundColor("#512DA8");
      VKSecureKeypadPlugin.setKeyboardTextColor("#FFFFFF");
      VKSecureKeypadPlugin.setKeyboardButtonBackgroundColor("#9575CD");
    }
    ```

## SSL Implementation

1. SSL Pinning is enabled and it is controlled by `allowsArbitraryNetworking()` API. Set **true** to disable it.
    > Remember to add `VGuardPlugin.allowsArbitraryNetworking();` before invoking `VGuardPlugin.setupVGuard();`.
2. If SSL Pinning is enabled, to bypass a URL, you need to get the certification and then include it in your profile on `V-OS App Protection Server`.
- Use the command to get the `certification`: 
  - > openssl s_client -showcerts -connect www.v-key.com:443
    - Get from `-----BEGIN CERTIFICATE-----` to `-----END CERTIFICATE-----`.
    - Put it to your profile on `V-OS App Protection Server`.

## Secure RESTful APIs
**To use the Secure RESTful APIs, import the VGSecureHttpUrlConnection component from react-native-vguard to the top portion of app.js file as follows:**
```js
import {VGSecureHttpUrlConnection} from 'react-native-vguard'
```

**You can use the example codes:**
```js
var hostname = "https://domain.come";
var endpt = "/webservice";
var contentType = "multipart/form-data";
var data = "The quick brown fox jumps over the lazy dog."
var requestAuthorizationHeader = null;
var timeoutInMilliseconds = 15000;
```

- **HTTP GET**
  ```js
  VGSecureHttpUrlConnection.get_urlconnection(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds)
      .then((response) => {
          console.log("responseCode: " + response.responseCode);
          console.log("response: " + response.response);
      })
      .catch((error) => {
          console.error('error.message:', error.message);
      });
  ```
- **HTTP POST**
  ```js
  VGSecureHttpUrlConnection.post_urlconnection(hostname, endpt, contentType, data, requestAuthorizationHeader, timeoutInMilliseconds)
    .then((response) => {
        console.log("responseCode: " + response.responseCode);
        console.log("response: " + response.response);
    })
    .catch((error) => {
        console.error('error.message:', error.message);
    });
  ```
- **HTTP DELETE**
  ```js
  VGSecureHttpUrlConnection.delete_urlconnection(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds)
    .then((response) => {
        console.log("responseCode: " + response.responseCode);
        console.log("response: " + response.response);
    })
    .catch((error) => {
        console.error('error.message:', error.message);
    });
  ```
- **HTTP HEAD**
  ```js
  VGSecureHttpUrlConnection.head_urlconnection(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds)
    .then((response) => {
        console.log("responseCode: " + response.responseCode);
        console.log("response: " + response.response);
    })
    .catch((error) => {
        console.error('error.message:', error.message);
    });
  ```
- **HTTP OPTIONS**
  ```js
  VGSecureHttpUrlConnection.put_urlconnection(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds)
    .then((response) => {
        console.log("responseCode: " + response.responseCode);
        console.log("response: " + response.response);
    })
    .catch((error) => {
        console.error('error.message:', error.message);
    });
  ```
- **HTTP PUT**
  ```js
  VGSecureHttpUrlConnection.put_urlconnection(hostname, endpt, contentType, data, requestAuthorizationHeader, timeoutInMilliseconds)
    .then((response) => {
        console.log("responseCode: " + response.responseCode);
        console.log("response: " + response.response);
    })
    .catch((error) => {
        console.error('error.message:', error.message);
    });
  ```
- **HTTP TRACE**
  ```js
  VGSecureHttpUrlConnection.trace_urlconnection(hostname, endpt, requestAuthorizationHeader, timeoutInMilliseconds)
    .then((response) => {
        console.log("responseCode: " + response.responseCode);
        console.log("response: " + response.response);
    })
    .catch((error) => {
        console.error('error.message:', error.message);
    });
  ```