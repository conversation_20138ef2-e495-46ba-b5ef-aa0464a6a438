# 4.10.5-AEON - What's new?
- Implement secureKeyboard for React Native 0.79
# 4.10.4-AEON - What's new?
- Remove secureKeyboard feature.
- Upgrade to React Native 0.79.
- Implement new detections of new SDKs: 4.10.4-CLT-001.
- Update source code to compatible with new SDKs.
- Add new API:
	+ getIsVGuardStarted
	+ getSecurityStatus
# 4.9.3 - What's new?
- Add excludeFilesFromIOSBackup logic for iOS
- Add events with action name:
	+ VGuardPlugin.VGUARD_SCREEN_SHARING_DETECTED
- Add new API for iOS:
	+ destroy
	+ requestScan
	+ setupVGuard
	+ setDebugable
	+ setAllowsArbitraryNetworking
- Increase plugin version 1.0.1

# 4.9.2 - What's new?
- Remove buildToolsVersion '33' because it is deprecated
- Use the new way to invoke the VGuard.
- Replace LifecycleEventListener with Application.ActivityLifecycleCallbacks so that we can fix the lifecycle issue.
- Add arrow functions because we use this keyword to fix the undefined issue when focusing on the Secure Keyboard.
- Updated the README.md file following the new way.
- Move the AAR file to the folder to fix the issue that can't build the APK release.
- Fix the issue that can't run on iOS.

# 4.9.1 - What's new?
- Add APIs:
  	+ sdkVersion()
  	+ getFirmwareVersion()
    + getProcessorVersion()
- Add events with action name:
	+ VGuardPlugin.VGUARD_OVERLAY_DETECTED_DISABLE
	+ VGuardPlugin.VGUARD_SCREEN_SHARING_DETECTED 
	## Usage: read more in README.md
	
# 4.9.0 - What's New?
- Update to be compatible with sdk 4.9.1 and later
- Add APIs: 
	+ setLoggerBaseUrl(String url)	//  This API sets the base URL of the V-OS Troubleshooting Logs Service.
	+ forceSyncLogs() 				//  This API  lets you manually synchornize the troubleshooting logs at the client-side to the V-OS Troubleshooting Logs Service.
	+ setMemoryConfiguration(int config) // 0: DEFAULT, 1: HIGH
	+ setThreatIntelligenceServerURL(String threadIntelUrl)
	+ setMaximumNetworkRetryTime(int retry)
	+ clearVOSTrustedStorage()		//  This API can be used while there is an issue reading from V-OS trusted storage (20035: ERROR_VOS_TRUST_STORAGE_FAILED)
	+ resetVOSTrustedStorage()		//  This API can be used when the SDK encounters 20035: ERROR_VOS_TRUST_STORAGE_FAILED result code.
	
- Add events with action name: 
	+ VGuardPlugin.RESET_VOS_STORAGE
	+ VGuardPlugin.VGUARD_VIRTUAL_SPACE_DETECTED
	+ VGuardPlugin.VGUARD_SSL_ERROR_DETECTED
	+ VGuardPlugin.VGUARD_HANDLE_THREAT_POLICY
  	## Usage: read more in README.md
	
# Resolved issue(s)
- Fix issue The keyboard not being closed if this is showed by user touch on input text
- Fix issue The keyboard not being closed if autofocus = true text

# Resolved issue(s)
- Update to be compatible with vguard component (vos-app-protection-android-4.8.7.0.aar - sdk 4.8 patch 14 hf3)

# Resolved issues in 4.8.0
- Update to be compatible with sdk 4.8.x
- [AP-263] [Android][React][UOB] SecureKeyboard


